# version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: order-management-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD:-secure_mongo_password_change_in_production}
      MONGO_INITDB_DATABASE: ${MONGODB_DATABASE:-order_management_db}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - order-management-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: order-management-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - order-management-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-secure_redis_password_change_in_production}

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: order-management-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      MONGODB_URI: mongodb://${MONGODB_ROOT_USERNAME:-admin}:${MONGODB_ROOT_PASSWORD:-secure_mongo_password_change_in_production}@mongodb:27017/${MONGODB_DATABASE:-order_management_db}?authSource=admin
      MONGODB_USERNAME: ${MONGODB_ROOT_USERNAME:-admin}
      MONGODB_PASSWORD: ${MONGODB_ROOT_PASSWORD:-secure_mongo_password_change_in_production}
      MONGODB_DATABASE: ${MONGODB_DATABASE:-order_management_db}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-secure_redis_password_change_in_production}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production-min-32-chars}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      BCRYPT_SALT_ROUNDS: ${BCRYPT_SALT_ROUNDS:-12}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your-32-char-encryption-key-change-in-production}
      API_KEY_SECRET: ${API_KEY_SECRET:-your-api-key-secret-change-in-production}
      SESSION_SECRET: ${SESSION_SECRET:-your-session-secret-change-in-production-min-32-chars}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      RATE_LIMIT_WINDOW_MS: ${RATE_LIMIT_WINDOW_MS:-900000}
      RATE_LIMIT_MAX_REQUESTS: ${RATE_LIMIT_MAX_REQUESTS:-100}
      EMAIL_SERVICE_API_KEY: ${EMAIL_SERVICE_API_KEY:-}
      EMAIL_FROM: ${EMAIL_FROM:-<EMAIL>}
      PAYMENT_GATEWAY_SECRET: ${PAYMENT_GATEWAY_SECRET:-}
      WEBHOOK_SECRET: ${WEBHOOK_SECRET:-}
    ports:
      - "3001:3001"
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - order-management-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: order-management-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - order-management-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx Load Balancer (Optional)
  nginx:
    image: nginx:alpine
    container_name: order-management-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - order-management-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  order-management-network:
    driver: bridge
